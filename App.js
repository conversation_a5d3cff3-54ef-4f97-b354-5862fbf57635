import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  Dimensions,
  TouchableOpacity,
  Animated,
  PanResponder,
  Platform,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Game constants
const BIRD_SIZE = 30;
const GRAVITY = 0.6;
const JUMP_STRENGTH = -12;
const PIPE_WIDTH = 60;
const PIPE_GAP = 200;
const PIPE_SPEED = 3;
const GROUND_HEIGHT = 100;

export default function App() {
  // Game state
  const [gameStarted, setGameStarted] = useState(false);
  const [gameOver, setGameOver] = useState(false);
  const [score, setScore] = useState(0);

  // Bird physics
  const [birdY, setBirdY] = useState(SCREEN_HEIGHT / 2);
  const [birdVelocity, setBirdVelocity] = useState(0);

  // Pipes
  const [pipes, setPipes] = useState([]);

  // Animation refs
  const gameLoopRef = useRef();
  const birdRotation = useRef(new Animated.Value(0)).current;

  // Initialize game
  const initGame = () => {
    setBirdY(SCREEN_HEIGHT / 2);
    setBirdVelocity(0);
    setPipes([]);
    setScore(0);
    setGameOver(false);
    setGameStarted(false);

    // Reset bird rotation
    birdRotation.setValue(0);
  };

  // Start game
  const startGame = () => {
    if (gameOver) {
      initGame();
      return;
    }

    if (!gameStarted) {
      setGameStarted(true);
      // Create first pipe
      setPipes([{
        id: Date.now(),
        x: SCREEN_WIDTH,
        topHeight: Math.random() * (SCREEN_HEIGHT - PIPE_GAP - GROUND_HEIGHT - 100) + 50,
        passed: false,
      }]);
    }
  };

  // Bird jump
  const jump = () => {
    if (gameOver) {
      initGame();
      return;
    }

    if (!gameStarted) {
      startGame();
    }

    setBirdVelocity(JUMP_STRENGTH);

    // Animate bird rotation
    Animated.sequence([
      Animated.timing(birdRotation, {
        toValue: -0.3,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(birdRotation, {
        toValue: 0.5,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Game loop
  useEffect(() => {
    if (!gameStarted || gameOver) return;

    gameLoopRef.current = setInterval(() => {
      // Update bird physics
      setBirdY(prevY => {
        const newY = prevY + birdVelocity;

        // Check ground collision
        if (newY > SCREEN_HEIGHT - GROUND_HEIGHT - BIRD_SIZE) {
          setGameOver(true);
          return SCREEN_HEIGHT - GROUND_HEIGHT - BIRD_SIZE;
        }

        // Check ceiling collision
        if (newY < 0) {
          setGameOver(true);
          return 0;
        }

        return newY;
      });

      setBirdVelocity(prevVelocity => prevVelocity + GRAVITY);

      // Update pipes
      setPipes(prevPipes => {
        let newPipes = prevPipes.map(pipe => ({
          ...pipe,
          x: pipe.x - PIPE_SPEED,
        }));

        // Remove pipes that are off screen
        newPipes = newPipes.filter(pipe => pipe.x > -PIPE_WIDTH);

        // Add new pipe when needed
        const lastPipe = newPipes[newPipes.length - 1];
        if (!lastPipe || lastPipe.x < SCREEN_WIDTH - 200) {
          newPipes.push({
            id: Date.now(),
            x: SCREEN_WIDTH,
            topHeight: Math.random() * (SCREEN_HEIGHT - PIPE_GAP - GROUND_HEIGHT - 100) + 50,
            passed: false,
          });
        }

        // Check for score updates and collisions
        newPipes.forEach(pipe => {
          // Score update
          if (!pipe.passed && pipe.x + PIPE_WIDTH < SCREEN_WIDTH / 2 - BIRD_SIZE / 2) {
            pipe.passed = true;
            setScore(prevScore => prevScore + 1);
          }

          // Collision detection
          const birdLeft = SCREEN_WIDTH / 2 - BIRD_SIZE / 2;
          const birdRight = SCREEN_WIDTH / 2 + BIRD_SIZE / 2;
          const birdTop = birdY;
          const birdBottom = birdY + BIRD_SIZE;

          const pipeLeft = pipe.x;
          const pipeRight = pipe.x + PIPE_WIDTH;

          // Check if bird is within pipe's horizontal bounds
          if (birdRight > pipeLeft && birdLeft < pipeRight) {
            // Check collision with top pipe
            if (birdTop < pipe.topHeight) {
              setGameOver(true);
            }
            // Check collision with bottom pipe
            if (birdBottom > pipe.topHeight + PIPE_GAP) {
              setGameOver(true);
            }
          }
        });

        return newPipes;
      });
    }, 16); // ~60 FPS

    return () => {
      if (gameLoopRef.current) {
        clearInterval(gameLoopRef.current);
      }
    };
  }, [gameStarted, gameOver, birdY, birdVelocity]);

  // Keyboard support for web
  useEffect(() => {
    if (Platform.OS === 'web') {
      const handleKeyPress = (event) => {
        if (event.code === 'Space') {
          event.preventDefault();
          jump();
        }
      };

      document.addEventListener('keydown', handleKeyPress);
      return () => document.removeEventListener('keydown', handleKeyPress);
    }
  }, [gameStarted, gameOver]);

  // Pan responder for touch controls
  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onPanResponderGrant: jump,
  });

  return (
    <View style={styles.container} {...panResponder.panHandlers}>
      <StatusBar style="light" />

      {/* Sky background */}
      <View style={styles.sky} />

      {/* Pipes */}
      {pipes.map(pipe => (
        <View key={pipe.id}>
          {/* Top pipe */}
          <View
            style={[
              styles.pipe,
              {
                left: pipe.x,
                top: 0,
                height: pipe.topHeight,
              },
            ]}
          />
          {/* Bottom pipe */}
          <View
            style={[
              styles.pipe,
              {
                left: pipe.x,
                top: pipe.topHeight + PIPE_GAP,
                height: SCREEN_HEIGHT - pipe.topHeight - PIPE_GAP - GROUND_HEIGHT,
              },
            ]}
          />
        </View>
      ))}

      {/* Bird */}
      <Animated.View
        style={[
          styles.bird,
          {
            left: SCREEN_WIDTH / 2 - BIRD_SIZE / 2,
            top: birdY,
            transform: [{ rotate: birdRotation }],
          },
        ]}
      />

      {/* Ground */}
      <View style={styles.ground} />

      {/* Score */}
      <Text style={styles.score}>{score}</Text>

      {/* Game Over / Start Screen */}
      {(!gameStarted || gameOver) && (
        <View style={styles.overlay}>
          <Text style={styles.title}>
            {gameOver ? 'Game Over!' : 'Flappy Bird'}
          </Text>
          {gameOver && (
            <Text style={styles.finalScore}>Score: {score}</Text>
          )}
          <TouchableOpacity style={styles.button} onPress={jump}>
            <Text style={styles.buttonText}>
              {gameOver ? 'Play Again' : 'Start Game'}
            </Text>
          </TouchableOpacity>
          <Text style={styles.instructions}>
            {Platform.OS === 'web'
              ? 'Press SPACE or tap to flap'
              : 'Tap to flap'
            }
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#87CEEB', // Sky blue
  },
  sky: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: GROUND_HEIGHT,
    backgroundColor: '#87CEEB',
  },
  bird: {
    position: 'absolute',
    width: BIRD_SIZE,
    height: BIRD_SIZE,
    backgroundColor: '#FFD700', // Gold
    borderRadius: BIRD_SIZE / 2,
    borderWidth: 2,
    borderColor: '#FFA500', // Orange border
  },
  pipe: {
    position: 'absolute',
    width: PIPE_WIDTH,
    backgroundColor: '#228B22', // Forest green
    borderWidth: 2,
    borderColor: '#006400', // Dark green border
  },
  ground: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: GROUND_HEIGHT,
    backgroundColor: '#8B4513', // Saddle brown
    borderTopWidth: 3,
    borderTopColor: '#654321',
  },
  score: {
    position: 'absolute',
    top: 60,
    alignSelf: 'center',
    fontSize: 48,
    fontWeight: 'bold',
    color: 'white',
    textShadowColor: 'black',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 36,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 20,
    textAlign: 'center',
  },
  finalScore: {
    fontSize: 24,
    color: 'white',
    marginBottom: 30,
  },
  button: {
    backgroundColor: '#FFD700',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    marginBottom: 20,
  },
  buttonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  instructions: {
    fontSize: 16,
    color: 'white',
    textAlign: 'center',
    opacity: 0.8,
  },
});
