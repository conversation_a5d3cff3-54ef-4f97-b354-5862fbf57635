<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flappy Bird - React Native Web</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(to bottom, #87CEEB 0%, #4FD0E7 50%, #3FC1C9 100%);
            overflow: hidden;
        }
        #root {
            width: 100vw;
            height: 100vh;
        }
        .game-container {
            position: relative;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, #87CEEB 0%, #4FD0E7 50%, #3FC1C9 100%);
            overflow: hidden;
            cursor: pointer;
        }

        /* Header UI */
        .header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 120px;
            background: rgba(0, 0, 0, 0.3);
            z-index: 150;
            padding: 20px;
            box-sizing: border-box;
        }
        .header-title {
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        .level-badge {
            background: rgba(255, 255, 255, 0.3);
            color: #FFD700;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 16px;
        }
        .score-display {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 24px;
            font-weight: bold;
            min-width: 60px;
            text-align: center;
        }
        .stats-panel {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 8px;
        }
        .coin-display {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .coin-icon {
            width: 16px;
            height: 16px;
            background: #FFD700;
            border-radius: 50%;
            border: 2px solid #FFA500;
        }
        .best-score {
            background: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
        }
        .achievement-badge {
            background: rgba(0, 0, 0, 0.7);
            color: #FFD700;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .medal-icon {
            width: 16px;
            height: 16px;
            background: #CD7F32;
            border-radius: 50%;
            border: 2px solid #8B4513;
        }

        /* Clouds */
        .cloud {
            position: absolute;
            background: white;
            border-radius: 50px;
            opacity: 0.8;
            animation: float 20s infinite linear;
        }
        .cloud:before {
            content: '';
            position: absolute;
            background: white;
            border-radius: 50px;
        }
        .cloud:after {
            content: '';
            position: absolute;
            background: white;
            border-radius: 50px;
        }
        .cloud1 {
            width: 80px;
            height: 40px;
            top: 150px;
            left: -100px;
        }
        .cloud1:before {
            width: 50px;
            height: 50px;
            top: -25px;
            left: 10px;
        }
        .cloud1:after {
            width: 60px;
            height: 40px;
            top: -15px;
            right: 10px;
        }
        .cloud2 {
            width: 60px;
            height: 30px;
            top: 200px;
            left: -80px;
            animation-delay: -10s;
        }
        .cloud2:before {
            width: 40px;
            height: 40px;
            top: -20px;
            left: 5px;
        }
        .cloud2:after {
            width: 50px;
            height: 30px;
            top: -10px;
            right: 5px;
        }
        @keyframes float {
            from { transform: translateX(0); }
            to { transform: translateX(calc(100vw + 200px)); }
        }

        .bird {
            position: absolute;
            width: 40px;
            height: 30px;
            z-index: 100;
            transition: transform 0.15s ease;
        }

        /* Bird body */
        .bird-body {
            position: absolute;
            width: 32px;
            height: 24px;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
            border-radius: 60% 40% 40% 60%;
            border: 2px solid #FF6B00;
            box-shadow: inset 2px 2px 4px rgba(255, 255, 255, 0.3),
                        inset -2px -2px 4px rgba(0, 0, 0, 0.2);
            left: 4px;
            top: 3px;
        }

        /* Bird head */
        .bird-head {
            position: absolute;
            width: 20px;
            height: 18px;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            border-radius: 50%;
            border: 2px solid #FF6B00;
            left: 18px;
            top: 0px;
            box-shadow: inset 1px 1px 2px rgba(255, 255, 255, 0.4);
        }

        /* Bird beak */
        .bird-beak {
            position: absolute;
            width: 0;
            height: 0;
            border-left: 8px solid #FF4500;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
            left: 36px;
            top: 10px;
            filter: drop-shadow(1px 1px 1px rgba(0, 0, 0, 0.3));
        }

        /* Bird eye */
        .bird-eye {
            position: absolute;
            width: 6px;
            height: 6px;
            background: white;
            border-radius: 50%;
            border: 1px solid #333;
            left: 22px;
            top: 6px;
        }
        .bird-eye:after {
            content: '';
            position: absolute;
            width: 3px;
            height: 3px;
            background: black;
            border-radius: 50%;
            top: 1px;
            left: 2px;
        }

        /* Bird wing */
        .bird-wing {
            position: absolute;
            width: 18px;
            height: 12px;
            background: linear-gradient(45deg, #FF8C00 0%, #FF6B00 100%);
            border-radius: 50% 20% 80% 50%;
            border: 1px solid #FF4500;
            left: 8px;
            top: 8px;
            transform-origin: 20% 50%;
            animation: wingFlap 0.3s ease-in-out infinite alternate;
        }

        /* Wing flapping animation */
        @keyframes wingFlap {
            0% { transform: rotate(-10deg) scaleY(1); }
            100% { transform: rotate(10deg) scaleY(0.8); }
        }

        /* Bird states */
        .bird.flying .bird-wing {
            animation-duration: 0.15s;
        }

        .bird.falling .bird-wing {
            animation-duration: 0.4s;
            opacity: 0.8;
        }

        /* Add subtle shadow under bird */
        .bird:before {
            content: '';
            position: absolute;
            width: 30px;
            height: 8px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 50%;
            left: 5px;
            top: 35px;
            filter: blur(3px);
            z-index: -1;
        }

        /* Bird tail */
        .bird-tail {
            position: absolute;
            width: 12px;
            height: 8px;
            background: linear-gradient(45deg, #FF6B00 0%, #FF4500 100%);
            border-radius: 0% 80% 80% 0%;
            border: 1px solid #FF4500;
            left: -2px;
            top: 10px;
            transform: rotate(-15deg);
        }

        /* Bird belly highlight */
        .bird-belly {
            position: absolute;
            width: 20px;
            height: 12px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
            border-radius: 50%;
            left: 10px;
            top: 12px;
        }

        .pipe {
            position: absolute;
            width: 60px;
            background: linear-gradient(to right, #228B22, #32CD32, #228B22);
            border: 3px solid #006400;
            border-radius: 8px;
        }
        .pipe:before {
            content: '';
            position: absolute;
            width: 70px;
            height: 25px;
            background: linear-gradient(to right, #228B22, #32CD32, #228B22);
            border: 3px solid #006400;
            border-radius: 8px;
            left: -8px;
        }
        .pipe-top:before {
            bottom: -3px;
        }
        .pipe-bottom:before {
            top: -3px;
        }

        .ground {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: linear-gradient(to bottom, #8B4513, #A0522D);
            border-top: 4px solid #654321;
        }
        /* Bottom UI */
        .bottom-ui {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            background: rgba(0, 0, 0, 0.3);
            z-index: 150;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-sizing: border-box;
        }
        .speed-indicator {
            background: rgba(139, 69, 19, 0.8);
            color: #FFD700;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        .settings-btn {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.3);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }
        .progress-bar {
            flex: 1;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            margin: 0 20px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: white;
            border-radius: 3px;
            width: 0%;
            transition: width 0.3s ease;
        }

        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 300;
        }
        .title {
            font-size: 42px;
            font-weight: bold;
            color: white;
            margin-bottom: 20px;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        .final-score {
            font-size: 28px;
            color: #FFD700;
            margin-bottom: 30px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }
        .button {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            padding: 18px 36px;
            border: none;
            border-radius: 30px;
            font-size: 22px;
            font-weight: bold;
            color: #333;
            cursor: pointer;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
            transition: all 0.3s ease;
        }
        .button:hover {
            background: linear-gradient(45deg, #FFC700, #FF8C00);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
        }
        .instructions {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
        }

        /* Floating coin animation */
        .floating-coin {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #FFD700;
            border: 2px solid #FFA500;
            border-radius: 50%;
            z-index: 200;
            animation: floatUp 1.5s ease-out forwards;
            pointer-events: none;
        }
        .floating-coin:before {
            content: '🪙';
            position: absolute;
            top: -2px;
            left: -1px;
            font-size: 16px;
        }
        @keyframes floatUp {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
            100% {
                opacity: 0;
                transform: translateY(-100px) scale(1.5);
            }
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="game-container" id="gameContainer">
            <!-- Header UI -->
            <div class="header">
                <div class="header-title">Flappy Bird</div>
                <div class="header-content">
                    <div class="level-badge" id="levelBadge">Level 1</div>
                    <div class="score-display" id="score">0</div>
                    <div class="stats-panel">
                        <div class="coin-display">
                            <div class="coin-icon"></div>
                            <span id="coinCount">0</span>
                        </div>
                        <div class="best-score" id="bestScore">Best: 0</div>
                        <div class="achievement-badge" id="achievementBadge">
                            <div class="medal-icon"></div>
                            <span>Bronze 2 (50🪙)</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Clouds -->
            <div class="cloud cloud1"></div>
            <div class="cloud cloud2"></div>

            <!-- Bird -->
            <div class="bird" id="bird">
                <div class="bird-tail"></div>
                <div class="bird-body"></div>
                <div class="bird-belly"></div>
                <div class="bird-wing"></div>
                <div class="bird-head"></div>
                <div class="bird-eye"></div>
                <div class="bird-beak"></div>
            </div>

            <!-- Ground -->
            <div class="ground"></div>

            <!-- Bottom UI -->
            <div class="bottom-ui">
                <div class="speed-indicator">Speed: 1.0x</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <button class="settings-btn">⚙️</button>
            </div>

            <!-- Game Overlay -->
            <div class="overlay" id="overlay">
                <div class="title" id="title">Flappy Bird</div>
                <div class="final-score" id="finalScore" style="display: none;"></div>
                <button class="button" id="startButton">Start Game</button>
                <div class="instructions">Press SPACE or click to flap</div>
            </div>
        </div>
    </div>

    <script>
        // Game constants
        const BIRD_SIZE = 40;
        const GRAVITY = 0.4;
        const JUMP_STRENGTH = -8;
        const PIPE_WIDTH = 60;
        const PIPE_GAP = 200;
        const PIPE_SPEED = 3;
        const GROUND_HEIGHT = 80;

        // Game state
        let gameStarted = false;
        let gameOver = false;
        let score = 0;
        let coins = 0;
        let bestScore = localStorage.getItem('bestScore') || 0;
        let level = 1;
        let birdY = window.innerHeight / 2;
        let birdVelocity = 0;
        let pipes = [];
        let gameLoop;
        let lastJumpTime = 0;
        let jumpCooldown = 150; // milliseconds

        // DOM elements
        const gameContainer = document.getElementById('gameContainer');
        const bird = document.getElementById('bird');
        let birdWing;
        const scoreElement = document.getElementById('score');
        const coinCountElement = document.getElementById('coinCount');
        const bestScoreElement = document.getElementById('bestScore');
        const levelBadgeElement = document.getElementById('levelBadge');
        const achievementBadgeElement = document.getElementById('achievementBadge');
        const progressFillElement = document.getElementById('progressFill');
        const overlay = document.getElementById('overlay');
        const title = document.getElementById('title');
        const finalScore = document.getElementById('finalScore');
        const startButton = document.getElementById('startButton');

        // Initialize UI
        function updateUI() {
            scoreElement.textContent = score;
            coinCountElement.textContent = coins;
            bestScoreElement.textContent = `Best: ${bestScore}`;
            levelBadgeElement.textContent = `Level ${level}`;

            // Update progress bar (based on score progress to next level)
            const progressPercent = Math.min((score % 10) * 10, 100);
            progressFillElement.style.width = `${progressPercent}%`;

            // Update achievement badge
            let achievement = 'Bronze 2';
            let requiredCoins = 50;
            if (coins >= 200) {
                achievement = 'Gold 1';
                requiredCoins = 500;
            } else if (coins >= 100) {
                achievement = 'Silver 3';
                requiredCoins = 200;
            } else if (coins >= 50) {
                achievement = 'Silver 1';
                requiredCoins = 100;
            }
            achievementBadgeElement.innerHTML = `<div class="medal-icon"></div><span>${achievement} (${requiredCoins}🪙)</span>`;
        }

        // Initialize game
        function initGame() {
            birdY = window.innerHeight / 2;
            birdVelocity = 0;
            pipes = [];
            score = 0;
            gameOver = false;
            gameStarted = false;
            level = Math.floor(coins / 50) + 1;

            // Clear pipes
            document.querySelectorAll('.pipe').forEach(pipe => pipe.remove());

            // Reset bird position
            updateBirdPosition();
            updateUI();

            // Show overlay
            overlay.style.display = 'flex';
            title.textContent = 'Flappy Bird';
            finalScore.style.display = 'none';
            startButton.textContent = 'Start Game';
        }

        // Start game
        function startGame() {
            if (gameOver) {
                initGame();
                return;
            }
            
            if (!gameStarted) {
                gameStarted = true;
                overlay.style.display = 'none';
                
                // Create first pipe
                createPipe();
                
                // Start game loop
                gameLoop = setInterval(updateGame, 16); // ~60 FPS
            }
        }

        // Bird jump
        function jump() {
            const currentTime = Date.now();

            if (gameOver) {
                initGame();
                return;
            }

            if (!gameStarted) {
                startGame();
                return;
            }

            // Add jump cooldown to prevent rapid jumping
            if (currentTime - lastJumpTime < jumpCooldown) {
                return;
            }

            // More stable jump - cap the velocity to prevent excessive upward movement
            if (birdVelocity > -4) {
                birdVelocity = JUMP_STRENGTH;
            } else {
                birdVelocity = Math.max(birdVelocity - 3, JUMP_STRENGTH);
            }

            // Add wing flapping effect
            triggerWingFlap();

            lastJumpTime = currentTime;
        }

        // Trigger wing flapping animation
        function triggerWingFlap() {
            if (!birdWing) {
                birdWing = bird.querySelector('.bird-wing');
            }

            if (birdWing) {
                // Temporarily speed up wing animation
                birdWing.style.animationDuration = '0.15s';
                setTimeout(() => {
                    birdWing.style.animationDuration = '0.3s';
                }, 300);
            }
        }

        // Create pipe
        function createPipe() {
            const pipeId = Date.now();
            const topHeight = Math.random() * (window.innerHeight - PIPE_GAP - GROUND_HEIGHT - 200) + 120;

            // Top pipe
            const topPipe = document.createElement('div');
            topPipe.className = 'pipe pipe-top';
            topPipe.id = `pipe-top-${pipeId}`;
            topPipe.style.left = `${window.innerWidth}px`;
            topPipe.style.top = '0px';
            topPipe.style.height = `${topHeight}px`;
            gameContainer.appendChild(topPipe);

            // Bottom pipe
            const bottomPipe = document.createElement('div');
            bottomPipe.className = 'pipe pipe-bottom';
            bottomPipe.id = `pipe-bottom-${pipeId}`;
            bottomPipe.style.left = `${window.innerWidth}px`;
            bottomPipe.style.top = `${topHeight + PIPE_GAP}px`;
            bottomPipe.style.height = `${window.innerHeight - topHeight - PIPE_GAP - GROUND_HEIGHT}px`;
            gameContainer.appendChild(bottomPipe);

            pipes.push({
                id: pipeId,
                x: window.innerWidth,
                topHeight: topHeight,
                passed: false
            });
        }

        // Update bird position
        function updateBirdPosition() {
            bird.style.left = `${window.innerWidth / 2 - BIRD_SIZE / 2}px`;
            bird.style.top = `${birdY}px`;

            // More stable rotation based on velocity
            const rotation = Math.max(-20, Math.min(45, birdVelocity * 4));
            bird.style.transform = `rotate(${rotation}deg)`;

            // Update bird state classes for different animations
            bird.classList.remove('flying', 'falling');
            if (birdVelocity < -2) {
                bird.classList.add('flying');
            } else if (birdVelocity > 2) {
                bird.classList.add('falling');
            }
        }

        // Create floating coin animation
        function createFloatingCoin(amount) {
            for (let i = 0; i < amount; i++) {
                const coin = document.createElement('div');
                coin.className = 'floating-coin';
                coin.style.left = `${window.innerWidth / 2 + (i - amount/2) * 30}px`;
                coin.style.top = `${birdY + 20}px`;
                gameContainer.appendChild(coin);

                // Remove coin after animation
                setTimeout(() => {
                    if (coin.parentNode) {
                        coin.parentNode.removeChild(coin);
                    }
                }, 1500);
            }
        }

        // Game loop
        function updateGame() {
            if (!gameStarted || gameOver) return;
            
            // Update bird physics
            birdY += birdVelocity;
            birdVelocity += GRAVITY;

            // Cap maximum falling speed for more stable gameplay
            birdVelocity = Math.min(birdVelocity, 8);
            
            // Check ground collision
            if (birdY > window.innerHeight - GROUND_HEIGHT - BIRD_SIZE) {
                endGame();
                return;
            }
            
            // Check ceiling collision
            if (birdY < 0) {
                endGame();
                return;
            }
            
            updateBirdPosition();
            
            // Update pipes
            pipes = pipes.filter(pipe => {
                pipe.x -= PIPE_SPEED;
                
                // Update pipe positions
                const topPipe = document.getElementById(`pipe-top-${pipe.id}`);
                const bottomPipe = document.getElementById(`pipe-bottom-${pipe.id}`);
                
                if (topPipe && bottomPipe) {
                    topPipe.style.left = `${pipe.x}px`;
                    bottomPipe.style.left = `${pipe.x}px`;
                }
                
                // Remove pipes that are off screen
                if (pipe.x < -PIPE_WIDTH) {
                    if (topPipe) topPipe.remove();
                    if (bottomPipe) bottomPipe.remove();
                    return false;
                }
                
                // Check for score and coins
                if (!pipe.passed && pipe.x + PIPE_WIDTH < window.innerWidth / 2 - BIRD_SIZE / 2) {
                    pipe.passed = true;
                    score++;
                    const coinsEarned = Math.floor(Math.random() * 3) + 1; // 1-3 coins per pipe
                    coins += coinsEarned;

                    // Create floating coin animation
                    createFloatingCoin(coinsEarned);

                    // Level up every 10 points
                    if (score % 10 === 0) {
                        level++;
                    }

                    updateUI();
                }
                
                // Check collision
                const birdLeft = window.innerWidth / 2 - BIRD_SIZE / 2;
                const birdRight = window.innerWidth / 2 + BIRD_SIZE / 2;
                const birdTop = birdY;
                const birdBottom = birdY + BIRD_SIZE;
                
                const pipeLeft = pipe.x;
                const pipeRight = pipe.x + PIPE_WIDTH;
                
                // Check if bird is within pipe's horizontal bounds
                if (birdRight > pipeLeft && birdLeft < pipeRight) {
                    // Check collision with top pipe
                    if (birdTop < pipe.topHeight) {
                        endGame();
                        return true;
                    }
                    // Check collision with bottom pipe
                    if (birdBottom > pipe.topHeight + PIPE_GAP) {
                        endGame();
                        return true;
                    }
                }
                
                return true;
            });
            
            // Add new pipe when needed
            const lastPipe = pipes[pipes.length - 1];
            if (!lastPipe || lastPipe.x < window.innerWidth - 200) {
                createPipe();
            }
        }

        // End game
        function endGame() {
            gameOver = true;
            clearInterval(gameLoop);

            // Update best score
            if (score > bestScore) {
                bestScore = score;
                localStorage.setItem('bestScore', bestScore);
            }

            // Save coins
            localStorage.setItem('coins', coins);

            // Show game over screen
            overlay.style.display = 'flex';
            title.textContent = 'Game Over!';
            finalScore.innerHTML = `
                <div>Score: ${score}</div>
                <div style="font-size: 18px; margin-top: 10px;">Coins Earned: +${Math.floor(score / 2)}</div>
            `;
            finalScore.style.display = 'block';
            startButton.textContent = 'Play Again';

            // Add bonus coins based on score
            coins += Math.floor(score / 2);
            updateUI();
        }

        // Event listeners
        startButton.addEventListener('click', jump);
        gameContainer.addEventListener('click', jump);
        
        document.addEventListener('keydown', (event) => {
            if (event.code === 'Space') {
                event.preventDefault();
                jump();
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (!gameStarted) {
                birdY = window.innerHeight / 2;
                updateBirdPosition();
            }
        });

        // Load saved data
        function loadSavedData() {
            coins = parseInt(localStorage.getItem('coins')) || 50; // Start with 50 coins
            bestScore = parseInt(localStorage.getItem('bestScore')) || 0;
        }

        // Initialize
        loadSavedData();
        initGame();
    </script>
</body>
</html>
